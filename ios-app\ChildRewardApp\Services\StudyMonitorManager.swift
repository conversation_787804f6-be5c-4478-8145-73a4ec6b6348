import AVFoundation
import Vision
import SwiftUI
import AVKit

class StudyMonitorManager: NSObject, ObservableObject {
    static let shared = StudyMonitorManager()
    
    // MARK: - Published Properties
    @Published var isMonitoring = false
    @Published var isStudentPresent = true
    @Published var headPoseStatus: HeadPoseStatus = .normal
    @Published var alertMessage: String?
    @Published var debugInfo: String = ""
    
    // MARK: - Private Properties
    private var captureSession: AVCaptureSession?
    private var videoOutput: AVCaptureVideoDataOutput?
    private var previewLayer: AVCaptureVideoPreviewLayer?
    
    // 行为分析
    private var lastValidDetectionTime = Date()
    private var alertTimer: Timer?
    private let alertThreshold: TimeInterval = 10 // 🔥 测试用：10秒（正式版改为300秒）
    
    // 语音合成
    private let speechSynthesizer = AVSpeechSynthesizer()
    
    enum HeadPoseStatus {
        case normal      // 正常朝向
        case turnedAway  // 头部转向
        case notDetected // 未检测到人脸
        
        var description: String {
            switch self {
            case .normal: return "正常"
            case .turnedAway: return "头部转向"
            case .notDetected: return "未检测到"
            }
        }
    }
    
    private override init() {
        super.init()
        print("🎥 StudyMonitorManager 初始化")
    }
}

// MARK: - 监控控制
extension StudyMonitorManager {
    
    func startMonitoring() {
        guard !isMonitoring else { 
            print("⚠️ 监控已在运行中")
            return 
        }
        
        print("🎥 开始学习监控（测试模式：10秒提醒）")
        DispatchQueue.main.async {
            self.debugInfo = "正在启动摄像头..."
        }
        
        requestCameraPermission { [weak self] granted in
            if granted {
                self?.setupCamera()
                DispatchQueue.main.async {
                    self?.isMonitoring = true
                    self?.lastValidDetectionTime = Date()
                    self?.startAlertTimer()
                    self?.debugInfo = "监控已启动"
                }
            } else {
                print("❌ 摄像头权限被拒绝")
                DispatchQueue.main.async {
                    self?.debugInfo = "摄像头权限被拒绝"
                }
            }
        }
    }
    
    func stopMonitoring() {
        print("🛑 停止学习监控")
        captureSession?.stopRunning()
        alertTimer?.invalidate()
        alertTimer = nil
        
        DispatchQueue.main.async {
            self.isMonitoring = false
            self.resetStatus()
            self.debugInfo = "监控已停止"
        }
    }
    
    private func requestCameraPermission(completion: @escaping (Bool) -> Void) {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            completion(true)
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { granted in
                completion(granted)
            }
        case .denied, .restricted:
            completion(false)
        @unknown default:
            completion(false)
        }
    }
    
    private func setupCamera() {
        captureSession = AVCaptureSession()
        captureSession?.sessionPreset = .medium
        
        guard let frontCamera = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .front),
              let input = try? AVCaptureDeviceInput(device: frontCamera) else {
            print("❌ 无法访问前置摄像头")
            DispatchQueue.main.async {
                self.debugInfo = "无法访问前置摄像头"
            }
            return
        }
        
        if captureSession?.canAddInput(input) == true {
            captureSession?.addInput(input)
        }
        
        videoOutput = AVCaptureVideoDataOutput()
        videoOutput?.setSampleBufferDelegate(self, queue: DispatchQueue(label: "camera.frame.processing"))
        
        if let videoOutput = videoOutput, captureSession?.canAddOutput(videoOutput) == true {
            captureSession?.addOutput(videoOutput)
        }
        
        DispatchQueue.global(qos: .background).async {
            self.captureSession?.startRunning()
            print("✅ 摄像头已启动")
        }
    }
}

// MARK: - 视频帧处理
extension StudyMonitorManager: AVCaptureVideoDataOutputSampleBufferDelegate {
    
    func captureOutput(_ output: AVCaptureOutput, didOutput sampleBuffer: CMSampleBuffer, from connection: AVCaptureConnection) {
        guard let pixelBuffer = CMSampleBufferGetImageBuffer(sampleBuffer) else { return }
        
        let request = VNDetectFaceRectanglesRequest { [weak self] request, error in
            self?.handleFaceDetection(request: request, error: error)
        }
        
        // 添加人脸关键点检测以获取头部姿态
        let landmarksRequest = VNDetectFaceLandmarksRequest { [weak self] request, error in
            self?.handleFaceLandmarks(request: request, error: error)
        }
        
        let handler = VNImageRequestHandler(cvPixelBuffer: pixelBuffer, options: [:])
        do {
            try handler.perform([request, landmarksRequest])
        } catch {
            print("❌ Vision处理错误: \(error)")
        }
    }
    
    private func handleFaceDetection(request: VNRequest, error: Error?) {
        if let error = error {
            print("❌ 人脸检测错误: \(error)")
            return
        }
        
        guard let results = request.results as? [VNFaceObservation] else {
            DispatchQueue.main.async {
                self.updateStudentStatus(.notDetected)
            }
            return
        }
        
        if results.isEmpty {
            DispatchQueue.main.async {
                self.updateStudentStatus(.notDetected)
            }
        } else {
            // 检测到人脸，分析头部姿态
            analyzeFacePose(results.first!)
        }
    }
    
    private func handleFaceLandmarks(request: VNRequest, error: Error?) {
        guard let results = request.results as? [VNFaceObservation] else { return }

        for face in results {
            if let landmarks = face.landmarks {
                // 通过面部关键点分析头部朝向
                analyzeHeadOrientation(landmarks: landmarks, boundingBox: face.boundingBox)
            }
        }
    }
}

// MARK: - 行为分析
extension StudyMonitorManager {

    private func analyzeFacePose(_ faceObservation: VNFaceObservation) {
        let boundingBox = faceObservation.boundingBox

        // 简单的头部姿态判断：基于人脸在画面中的位置
        let centerX = boundingBox.midX
        let centerY = boundingBox.midY

        // 判断是否在正常范围内（画面中央区域）
        let normalRange: ClosedRange<CGFloat> = 0.25...0.75

        let isNormalPose = normalRange.contains(centerX) && normalRange.contains(centerY)

        DispatchQueue.main.async {
            if isNormalPose {
                self.updateStudentStatus(.normal)
            } else {
                self.updateStudentStatus(.turnedAway)
            }
        }
    }

    private func analyzeHeadOrientation(landmarks: VNFaceLandmarks2D, boundingBox: CGRect) {
        // 更精确的头部姿态分析
        guard let nose = landmarks.nose,
              let leftEye = landmarks.leftEye,
              let rightEye = landmarks.rightEye else { return }

        // 计算眼睛和鼻子的相对位置来判断头部转向
        let nosePoints = nose.normalizedPoints
        let leftEyePoints = leftEye.normalizedPoints
        let rightEyePoints = rightEye.normalizedPoints

        if !nosePoints.isEmpty && !leftEyePoints.isEmpty && !rightEyePoints.isEmpty {
            // 简化的头部朝向判断逻辑
            let eyeDistance = distance(leftEyePoints[0], rightEyePoints[0])
            let isHeadTurnedAway = eyeDistance < 0.05 // 阈值可调整

            DispatchQueue.main.async {
                self.updateStudentStatus(isHeadTurnedAway ? .turnedAway : .normal)
            }
        }
    }

    private func distance(_ point1: CGPoint, _ point2: CGPoint) -> CGFloat {
        return sqrt(pow(point1.x - point2.x, 2) + pow(point1.y - point2.y, 2))
    }

    private func updateStudentStatus(_ status: HeadPoseStatus) {
        headPoseStatus = status

        switch status {
        case .normal:
            isStudentPresent = true
            lastValidDetectionTime = Date()
            debugInfo = "检测正常 ✅"
        case .turnedAway:
            isStudentPresent = false
            debugInfo = "头部转向 ⚠️"
        case .notDetected:
            isStudentPresent = false
            debugInfo = "未检测到人脸 ❌"
        }
    }
}

// MARK: - 提醒系统
extension StudyMonitorManager {

    private func startAlertTimer() {
        alertTimer = Timer.scheduledTimer(withTimeInterval: 2, repeats: true) { [weak self] _ in
            self?.checkStudyStatus()
        }
        print("⏰ 提醒定时器已启动（每2秒检查一次）")
    }

    private func checkStudyStatus() {
        let timeElapsed = Date().timeIntervalSince(lastValidDetectionTime)

        DispatchQueue.main.async {
            self.debugInfo = "状态: \(self.headPoseStatus.description) | 已过时间: \(Int(timeElapsed))秒"
        }

        if timeElapsed >= alertThreshold {
            triggerAlert()
        }
    }

    private func triggerAlert() {
        let alertMessages = [
            "小朋友，请专心学习哦！",
            "注意力要集中，继续加油！",
            "请回到书桌前认真学习",
            "学习时间到了，请专心完成任务"
        ]

        let message = alertMessages.randomElement() ?? alertMessages[0]

        DispatchQueue.main.async {
            self.alertMessage = message
            self.speakAlert(message)
            print("🔔 触发提醒: \(message)")
        }

        // 重置计时器，避免频繁提醒（60秒后再次检查）
        lastValidDetectionTime = Date().addingTimeInterval(-alertThreshold + 60)
    }

    private func speakAlert(_ message: String) {
        // 停止当前播放
        if speechSynthesizer.isSpeaking {
            speechSynthesizer.stopSpeaking(at: .immediate)
        }

        let utterance = AVSpeechUtterance(string: message)
        utterance.voice = AVSpeechSynthesisVoice(language: "zh-CN")
        utterance.rate = 0.5
        utterance.volume = 0.8

        speechSynthesizer.speak(utterance)
    }

    private func resetStatus() {
        isStudentPresent = true
        headPoseStatus = .normal
        alertMessage = nil
        debugInfo = ""
    }

    func clearAlert() {
        alertMessage = nil
    }
}
