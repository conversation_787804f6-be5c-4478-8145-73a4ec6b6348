import SwiftUI

struct StudyMonitorIndicator: View {
    @ObservedObject var monitor = StudyMonitorManager.shared
    
    var body: some View {
        VStack(spacing: 8) {
            // 主要状态指示器
            HStack(spacing: 12) {
                // 监控状态指示器
                HStack(spacing: 6) {
                    Circle()
                        .fill(monitor.isMonitoring ? Color.green : Color.gray)
                        .frame(width: 8, height: 8)
                    
                    Text(monitor.isMonitoring ? "学习监控中" : "监控已停止")
                        .font(.caption)
                        .foregroundColor(.primary)
                }
                
                // 学生状态指示
                if monitor.isMonitoring {
                    HStack(spacing: 4) {
                        Image(systemName: monitor.isStudentPresent ? "person.fill" : "person.slash")
                            .foregroundColor(monitor.isStudentPresent ? .green : .orange)
                            .font(.caption)
                        
                        Text(monitor.headPoseStatus.description)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            // 调试信息（测试阶段显示）
            if monitor.isMonitoring && !monitor.debugInfo.isEmpty {
                Text(monitor.debugInfo)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            // 提醒消息
            if let alertMessage = monitor.alertMessage {
                HStack(spacing: 8) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.orange)
                        .font(.caption)
                    
                    Text(alertMessage)
                        .font(.caption)
                        .foregroundColor(.orange)
                        .multilineTextAlignment(.leading)
                    
                    Button("知道了") {
                        monitor.clearAlert()
                    }
                    .font(.caption2)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.orange.opacity(0.2))
                    .cornerRadius(8)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(Color.orange.opacity(0.1))
                .cornerRadius(12)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(.ultraThinMaterial)
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

// 简化版本的监控指示器（用于右侧面板）
struct CompactStudyMonitorIndicator: View {
    @ObservedObject var monitor = StudyMonitorManager.shared
    
    var body: some View {
        VStack(spacing: 4) {
            HStack(spacing: 6) {
                Circle()
                    .fill(monitor.isMonitoring ? Color.green : Color.gray)
                    .frame(width: 6, height: 6)
                
                Text("监控")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            if monitor.isMonitoring {
                Image(systemName: monitor.isStudentPresent ? "person.fill" : "person.slash")
                    .foregroundColor(monitor.isStudentPresent ? .green : .orange)
                    .font(.caption2)
            }
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 6)
        .background(.ultraThinMaterial)
        .cornerRadius(8)
    }
}

#Preview {
    VStack(spacing: 20) {
        StudyMonitorIndicator()
        CompactStudyMonitorIndicator()
    }
    .padding()
    .background(Color(.systemGroupedBackground))
}
